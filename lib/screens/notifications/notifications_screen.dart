import 'package:flutter/material.dart';
import 'package:packagingwala/widgets/custom_app_bar.dart';
import 'package:packagingwala/constants/app_colors.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            CustomAppBar(
              title: 'Notifications',
              showBackButton: true,
            ),
            
            // Scrollable Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Today Section
                    _buildSectionHeader('Today'),
                    const SizedBox(height: 16),
                    
                    _buildNotificationItem(
                      icon: Icons.inventory_2,
                      iconColor: AppColors.primaryColor,
                      title: 'New Order Received',
                      description: 'Order #0001 has been placed by k<PERSON>hna. 3 items totaling ₹ 450',
                      time: '2 Min ago',
                    ),
                    
                    const SizedBox(height: 12),
                    
                    _buildNotificationItem(
                      icon: Icons.payment,
                      iconColor: AppColors.primaryColor,
                      title: 'Payment Confirmed',
                      description: 'Payment of ₹ 550 has been successfully Processed for order #0001',
                      time: '15 Min ago',
                    ),
                    
                    const SizedBox(height: 12),
                    
                    _buildNotificationItem(
                      icon: Icons.local_shipping,
                      iconColor: AppColors.primaryColor,
                      title: 'Order out for Delivery',
                      description: 'Order #0001 is now out for Delivery. Expected Delivery: 2:30 PM',
                      time: '1 Day ago',
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // Yesterday Section
                    _buildSectionHeader('Yesterday'),
                    const SizedBox(height: 16),
                    
                    _buildNotificationItem(
                      icon: Icons.check_circle,
                      iconColor: AppColors.primaryColor,
                      title: 'Order Completed',
                      description: 'Order #0001 has been successfully delivered and marked as complete',
                      time: '2 Day ago',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppColors.blackColor,
      ),
    );
  }

  Widget _buildNotificationItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String description,
    required String time,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon Container
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.blackColor,
                        ),
                      ),
                    ),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: AppColors.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          time,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.greyColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.greyColor,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
